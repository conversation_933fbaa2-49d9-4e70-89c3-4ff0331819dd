import React from 'react';
import { render, screen } from '@testing-library/react';
import { LazyDataSourceList, MutationOperation, ItemUpdateStrategy } from '../data-table2';
import { IBasemodel, IBasemodelController, ListResponse } from '@/models/base-model';

// Mock data and controller for testing
interface TestItem extends IBasemodel {
    id: string;
    name: string;
    status: string;
    lastChangeTimestamp: number;
    createdAt: number;
}

class MockController implements IBasemodelController<TestItem, any> {
    getId = (item: TestItem) => item.id;
    
    useList = jest.fn().mockReturnValue({
        data: {
            entries: [
                { id: '1', name: 'Item 1', status: 'active', lastChangeTimestamp: Date.now(), createdAt: Date.now() },
                { id: '2', name: 'Item 2', status: 'inactive', lastChangeTimestamp: Date.now(), createdAt: Date.now() }
            ],
            total: 10,
            nextToken: 'token123'
        } as ListResponse<TestItem>,
        isLoading: false,
        mutate: jest.fn()
    });
    
    useGet = jest.fn().mockReturnValue({
        data: undefined,
        error: false,
        isValidating: false,
        isLoading: false,
        mutate: jest.fn()
    });
    
    useDelete = jest.fn();
    can = jest.fn().mockReturnValue(true);
}

describe('LazyDataSourceList', () => {
    let controller: MockController;
    let dataSource: LazyDataSourceList<TestItem>;

    beforeEach(() => {
        controller = new MockController();
        dataSource = new LazyDataSourceList(controller);
    });

    test('should initialize with empty data', () => {
        expect(dataSource.getData()).toEqual([]);
        expect(dataSource.getTotalCount()).toBe(0);
        expect(dataSource.getLoadedCount()).toBe(0);
        expect(dataSource.isLoading()).toBe(false);
    });

    test('should update data from SWR', () => {
        const mockData: ListResponse<TestItem> = {
            entries: [
                { id: '1', name: 'Item 1', status: 'active', lastChangeTimestamp: Date.now(), createdAt: Date.now() }
            ],
            total: 5,
            nextToken: 'token123'
        };

        dataSource.updateFromSWR(mockData, false, jest.fn());

        expect(dataSource.getData()).toHaveLength(1);
        expect(dataSource.getTotalCount()).toBe(5);
        expect(dataSource.getLoadedCount()).toBe(1);
    });

    test('should replace item correctly', () => {
        const initialData: ListResponse<TestItem> = {
            entries: [
                { id: '1', name: 'Item 1', status: 'active', lastChangeTimestamp: Date.now(), createdAt: Date.now() }
            ],
            total: 1,
            nextToken: undefined
        };

        dataSource.updateFromSWR(initialData, false, jest.fn());

        const updatedItem: TestItem = {
            id: '1',
            name: 'Updated Item 1',
            status: 'active',
            lastChangeTimestamp: Date.now(),
            createdAt: Date.now()
        };

        dataSource.replaceItem(updatedItem);

        const data = dataSource.getData();
        expect(data[0].name).toBe('Updated Item 1');
    });

    test('should delete item correctly', () => {
        const initialData: ListResponse<TestItem> = {
            entries: [
                { id: '1', name: 'Item 1', status: 'active', lastChangeTimestamp: Date.now(), createdAt: Date.now() },
                { id: '2', name: 'Item 2', status: 'inactive', lastChangeTimestamp: Date.now(), createdAt: Date.now() }
            ],
            total: 2,
            nextToken: undefined
        };

        dataSource.updateFromSWR(initialData, false, jest.fn());

        dataSource.deleteItem('1');

        const data = dataSource.getData();
        expect(data).toHaveLength(1);
        expect(data[0].id).toBe('2');
        expect(dataSource.getTotalCount()).toBe(1);
    });

    test('should handle invalidation', () => {
        const initialData: ListResponse<TestItem> = {
            entries: [
                { id: '1', name: 'Item 1', status: 'active', lastChangeTimestamp: Date.now(), createdAt: Date.now() }
            ],
            total: 1,
            nextToken: undefined
        };

        dataSource.updateFromSWR(initialData, false, jest.fn());
        expect(dataSource.getData()).toHaveLength(1);

        dataSource.invalidate();

        expect(dataSource.getData()).toHaveLength(0);
        expect(dataSource.getTotalCount()).toBe(0);
    });

    test('should set configuration correctly', () => {
        dataSource.setMinItemsPerRequest(100);
        dataSource.setAutoLoadUntilExhaustion(true);
        dataSource.setItemUpdateInterval(5000);
        dataSource.setItemUpdateStrategy(ItemUpdateStrategy.MERGE);

        // These are internal settings, so we test through behavior
        expect(dataSource.getData()).toEqual([]);
    });

    test('should handle item update management', () => {
        const shouldInvalidate = (item: TestItem) => item.status === 'processing';
        dataSource.setShouldInvalidate(shouldInvalidate);

        const initialData: ListResponse<TestItem> = {
            entries: [
                { id: '1', name: 'Item 1', status: 'processing', lastChangeTimestamp: Date.now(), createdAt: Date.now() }
            ],
            total: 1,
            nextToken: undefined
        };

        dataSource.updateFromSWR(initialData, false, jest.fn());

        const itemsToUpdate = dataSource.getItemsToUpdate();
        expect(itemsToUpdate).toContain('1');
    });

    test('should handle item update from SWR', () => {
        const initialData: ListResponse<TestItem> = {
            entries: [
                { id: '1', name: 'Item 1', status: 'active', lastChangeTimestamp: Date.now(), createdAt: Date.now() }
            ],
            total: 1,
            nextToken: undefined
        };

        dataSource.updateFromSWR(initialData, false, jest.fn());

        const updatedItemData = [
            { id: '1', name: 'Updated Item 1', status: 'active', lastChangeTimestamp: Date.now(), createdAt: Date.now() }
        ];

        dataSource.updateItemFromSWR('1', updatedItemData, null);

        const data = dataSource.getData();
        expect(data[0].name).toBe('Updated Item 1');
    });
});

describe('Mutation Operations', () => {
    test('should have correct mutation operation values', () => {
        expect(MutationOperation.REPLACE).toBe('replace');
        expect(MutationOperation.MERGE).toBe('merge');
        expect(MutationOperation.DELETE).toBe('delete');
    });
});
