import { IBasemodel, IBasemodelController, ListResponse } from "@/models/base-model";
import { Empty, Table } from "antd";
import React, { useEffect } from "react";
import type { TableColumnsType } from 'antd';
import { mergeObjectsDeep } from "@/functions/object-helper";

// LazyDataSource interface for handling data loading and management
export interface LazyDataSource<T extends IBasemodel> {
    // Data access
    getData(): T[];
    getTotalCount(): number;
    getLoadedCount(): number;
    
    // Loading state
    isLoading(): boolean;
    
    // Data operations
    loadMore(): Promise<void>;
    loadUntilPage(page: number, pageSize: number): Promise<void>;
    loadAll(): Promise<void>;
    invalidate(): void;
    
    // Item mutations
    replaceItem(item: T): void;
    mergeItem(item: Partial<T>): void;
    deleteItem(id: string): void;
    
    // Configuration
    setMinItemsPerRequest(count: number): void;
    setAutoLoadUntilExhaustion(enabled: boolean): void;
    
    // Events
    onDataChange(callback: (data: T[]) => void): void;
    onLoadingChange(callback: (isLoading: boolean) => void): void;
}

// LazyDataSourceList implementation using IBasemodelController
export class LazyDataSourceList<T extends IBasemodel> implements LazyDataSource<T> {
    private controller: IBasemodelController<T, any>;
    private data: T[] = [];
    private totalCount: number = 0;
    private nextToken?: string;
    private loading: boolean = false;
    private minItemsPerRequest: number = 50;
    private autoLoadUntilExhaustion: boolean = false;
    private dataChangeCallback?: (data: T[]) => void;
    private loadingChangeCallback?: (isLoading: boolean) => void;
    private hasInitialLoad: boolean = false;
    private exhausted: boolean = false;
    private currentRequest?: { count: number; nextToken?: string };
    private mutateFunction?: any;

    constructor(controller: IBasemodelController<T, any>) {
        this.controller = controller;
    }

    // Method to be called from component with SWR result
    updateFromSWR(data: ListResponse<T> | undefined, isLoading: boolean, mutate: any): void {
        this.mutateFunction = mutate;

        if (isLoading !== this.loading) {
            this.loading = isLoading;
            this.loadingChangeCallback?.(isLoading);
        }

        if (data) {
            if (!this.hasInitialLoad) {
                // First load - set total count and reset data
                this.totalCount = data.total;
                this.hasInitialLoad = true;
                this.data = [...data.entries];
            } else if (this.currentRequest) {
                // Subsequent loads - append data only if this was a requested load
                this.data.push(...data.entries);
                this.currentRequest = undefined;
            }

            this.nextToken = data.nextToken;

            if (!this.nextToken) {
                this.exhausted = true;
                // Update total count to match actual loaded data if no more data
                this.totalCount = this.data.length;
            }

            this.notifyDataChange();

            // Auto-load if enabled and not exhausted
            if (this.autoLoadUntilExhaustion && !this.exhausted && !isLoading) {
                setTimeout(() => this.loadMore(), 0);
            }
        }
    }

    getCurrentRequest(): { count: number; nextToken?: string } | undefined {
        return this.currentRequest;
    }

    getData(): T[] {
        return this.data;
    }

    getTotalCount(): number {
        return this.totalCount;
    }

    getLoadedCount(): number {
        return this.data.length;
    }

    isLoading(): boolean {
        return this.loading;
    }

    private notifyDataChange(): void {
        this.dataChangeCallback?.(this.data);
    }

    async loadMore(): Promise<void> {
        if (this.loading || this.exhausted || this.currentRequest) return;

        this.currentRequest = {
            count: this.minItemsPerRequest,
            nextToken: this.nextToken
        };
    }

    async loadUntilPage(page: number, pageSize: number): Promise<void> {
        const requiredItems = page * pageSize;
        
        while (this.data.length < requiredItems && !this.exhausted && !this.currentRequest) {
            await this.loadMore();
            // Wait a bit for the request to be processed
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    async loadAll(): Promise<void> {
        this.setAutoLoadUntilExhaustion(true);
    }

    invalidate(): void {
        this.data = [];
        this.totalCount = 0;
        this.nextToken = undefined;
        this.hasInitialLoad = false;
        this.exhausted = false;
        this.currentRequest = undefined;
        this.notifyDataChange();
        
        // Trigger SWR revalidation if available
        if (this.mutateFunction) {
            this.mutateFunction();
        }
    }

    replaceItem(item: T): void {
        const id = this.controller.getId(item);
        const index = this.data.findIndex(existing => this.controller.getId(existing) === id);
        
        if (index >= 0) {
            this.data[index] = item;
        } else {
            this.data.push(item);
        }
        this.notifyDataChange();
    }

    mergeItem(item: Partial<T>): void {
        // Find item by trying to get ID from the partial item
        let id: string;
        try {
            id = this.controller.getId(item as T);
        } catch {
            console.error('Cannot merge item without valid ID');
            return;
        }

        const index = this.data.findIndex(existing => this.controller.getId(existing) === id);
        
        if (index >= 0) {
            this.data[index] = mergeObjectsDeep(this.data[index], item);
            this.notifyDataChange();
        }
    }

    deleteItem(id: string): void {
        const index = this.data.findIndex(item => this.controller.getId(item) === id);
        if (index >= 0) {
            this.data.splice(index, 1);
            this.totalCount = Math.max(0, this.totalCount - 1);
            this.notifyDataChange();
        }
    }

    setMinItemsPerRequest(count: number): void {
        this.minItemsPerRequest = count;
    }

    setAutoLoadUntilExhaustion(enabled: boolean): void {
        this.autoLoadUntilExhaustion = enabled;
        if (enabled && !this.exhausted && !this.loading && !this.currentRequest) {
            this.loadMore();
        }
    }

    onDataChange(callback: (data: T[]) => void): void {
        this.dataChangeCallback = callback;
    }

    onLoadingChange(callback: (isLoading: boolean) => void): void {
        this.loadingChangeCallback = callback;
    }
}

// Mutation operation types
export enum MutationOperation {
    REPLACE = 'replace',
    MERGE = 'merge',
    DELETE = 'delete'
}

export interface ItemMutation<T extends IBasemodel> {
    operation: MutationOperation;
    item?: T;
    partialItem?: Partial<T>;
    id?: string;
}

// DataTable2 parameters interface
export interface DataTable2Params<T extends IBasemodel> {
    dataSource: LazyDataSource<T>;
    itemUpdateInterval: number;
    tableColumns: TableColumnsType<T>;
    noDataDetails?: (isError: boolean) => React.JSX.Element;
    invalidate?: boolean;
    onItemsValidatingChange?: (isValidating: boolean, validatingIdList: string[]) => void;
    onDataChange?: (entries: T[]) => void;
    shouldInvalidate: (entry: T) => boolean;
    mutations?: ItemMutation<T>[];
}

// Hook to use LazyDataSourceList with SWR
export function useLazyDataSourceList<T extends IBasemodel>(
    controller: IBasemodelController<T, any>,
    minItemsPerRequest: number = 50
): LazyDataSourceList<T> {
    const [dataSource] = React.useState(() => new LazyDataSourceList(controller));
    const [request, setRequest] = React.useState<{ count: number; nextToken?: string }>({ count: minItemsPerRequest });

    // Configure data source
    React.useEffect(() => {
        dataSource.setMinItemsPerRequest(minItemsPerRequest);
    }, [dataSource, minItemsPerRequest]);

    // SWR hook for data fetching
    const { data, isLoading, mutate } = controller.useList(request);

    // Update data source when SWR data changes
    React.useEffect(() => {
        dataSource.updateFromSWR(data, isLoading, mutate);
    }, [data, isLoading, mutate, dataSource]);

    // Listen for new requests from data source
    React.useEffect(() => {
        const checkForNewRequest = () => {
            const newRequest = dataSource.getCurrentRequest();
            if (newRequest && JSON.stringify(newRequest) !== JSON.stringify(request)) {
                setRequest(newRequest);
            }
        };

        const interval = setInterval(checkForNewRequest, 50);
        return () => clearInterval(interval);
    }, [dataSource, request]);

    return dataSource;
}

// DataTable2 component with LazyDataSource integration
export function DataTable2<T extends IBasemodel>(params: DataTable2Params<T>) {
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [currentPage, setCurrentPage] = React.useState<number>(1);
    const [pageSize, setPageSize] = React.useState<number>(10);

    // Get controller from LazyDataSourceList if available
    const controller = (params.dataSource as any).controller;

    // Hook for updating pending items
    const { data: statusData, isValidating: statusIsLoading } = controller?.useGet(pendingData, {
        refreshInterval: params.itemUpdateInterval,
        revalidateIfStale: true
    }) || { data: undefined, isValidating: false };

    // Set up data source callbacks
    useEffect(() => {
        params.dataSource.onDataChange((data: T[]) => {
            if (params.onDataChange) {
                params.onDataChange(data);
            }
        });

        params.dataSource.onLoadingChange((_isLoading: boolean) => {
            // Loading state is handled through the data source
        });
    }, [params.dataSource, params.onDataChange]);

    // Handle invalidation
    useEffect(() => {
        if (params.invalidate !== undefined) {
            params.dataSource.invalidate();
        }
    }, [params.invalidate, params.dataSource]);

    // Handle mutations
    useEffect(() => {
        if (!params.mutations || params.mutations.length === 0) return;

        params.mutations.forEach(mutation => {
            switch (mutation.operation) {
                case MutationOperation.REPLACE:
                    if (mutation.item) {
                        params.dataSource.replaceItem(mutation.item);
                    }
                    break;
                case MutationOperation.MERGE:
                    if (mutation.partialItem) {
                        params.dataSource.mergeItem(mutation.partialItem);
                    }
                    break;
                case MutationOperation.DELETE:
                    if (mutation.id) {
                        params.dataSource.deleteItem(mutation.id);
                    }
                    break;
            }
        });
    }, [params.mutations, params.dataSource]);

    // Handle pending data updates
    useEffect(() => {
        if (params.onItemsValidatingChange) {
            params.onItemsValidatingChange(statusIsLoading, pendingData);
        }
    }, [statusIsLoading, pendingData, params.onItemsValidatingChange]);

    // Update pending data based on current data
    useEffect(() => {
        const data = params.dataSource.getData();
        const pendingDataUpdate = data.filter((value: T) => params.shouldInvalidate(value))
            .map((value: T) => controller?.getId(value)) ?? [];

        setPendingData(pendingData.filter((key: string) => pendingDataUpdate.includes(key)));

        const interval = setTimeout(() => {
            setPendingData(pendingDataUpdate);
        }, params.itemUpdateInterval);

        return () => clearTimeout(interval);
    }, [params.dataSource.getData(), params.shouldInvalidate, params.itemUpdateInterval, controller]);

    // Handle status data updates
    useEffect(() => {
        if (!statusData || !controller) return;

        const pendingDataUpdate = statusData.filter((value: T) => params.shouldInvalidate(value))
            .map((value: T) => controller.getId(value)) ?? [];
        const updatedItems = statusData.filter((value: T) => !params.shouldInvalidate(value)) ?? [];

        if (updatedItems.length > 0) {
            updatedItems.forEach((item: T) => {
                params.dataSource.replaceItem(item);
            });
        }

        if (pendingDataUpdate.length > 0 && statusData.filter((item: T) =>
            !pendingDataUpdate.includes(controller.getId(item))).length > 0) {
            const interval = setInterval(() => {
                if (pendingDataUpdate.length > 0) {
                    setPendingData(pendingDataUpdate);
                }
            }, params.itemUpdateInterval);
            return () => clearInterval(interval);
        }
    }, [statusData, params.shouldInvalidate, params.itemUpdateInterval, controller, params.dataSource]);

    // Handle pagination changes
    const handleTableChange = async (pagination: any) => {
        const newPage = pagination.current || 1;
        const newPageSize = pagination.pageSize || pageSize;

        setCurrentPage(newPage);
        setPageSize(newPageSize);

        // Load data until the requested page is available
        await params.dataSource.loadUntilPage(newPage, newPageSize);
    };

    // Initial data load
    useEffect(() => {
        if (params.dataSource.getData().length === 0 && !params.dataSource.isLoading()) {
            params.dataSource.loadMore();
        }
    }, [params.dataSource]);

    const data = params.dataSource.getData();
    const totalCount = params.dataSource.getTotalCount();
    const isLoading = params.dataSource.isLoading();

    return (
        <Table<T>
            rowSelection={{ type: 'checkbox' }}
            loading={isLoading}
            columns={params.tableColumns}
            dataSource={data}
            rowKey={(record: T) => controller?.getId(record) || 'id'}
            pagination={{
                position: ['bottomCenter'],
                current: currentPage,
                pageSize: pageSize,
                total: totalCount,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                    `${range[0]}-${range[1]} of ${total} items (${params.dataSource.getLoadedCount()} loaded)`
            }}
            onChange={handleTableChange}
            locale={{
                emptyText: <Empty description="No Data">
                    {params.noDataDetails ? params.noDataDetails(false) : null}
                </Empty>
            }}
        />
    );
}

export default DataTable2;
